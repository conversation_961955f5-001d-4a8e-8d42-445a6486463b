<div class="auth-container">
  <mat-card class="auth-card token-card">
    <!-- Header Section -->
    <div class="auth-header">
      <div class="auth-logo">
        <img
          src="logo/logo2.png"
          alt="Chattrix Logo"
          (error)="onImageError($event)"
          [style.display]="logoLoaded ? 'block' : 'none'"
        />
        <div
          class="logo-fallback"
          [style.display]="logoLoaded ? 'none' : 'flex'"
        >
          <span class="logo-text">C</span>
        </div>
      </div>
      <h1 class="auth-title">Verify Reset Code</h1>
      <p class="auth-subtitle">
        We've sent a 6-digit verification code to <strong>{{ email }}</strong
        >. Please enter it below to verify your identity.
      </p>
    </div>

    <!-- Token Form -->
    <form
      [formGroup]="tokenForm"
      (ngSubmit)="onVerifyToken()"
      class="auth-form"
    >
      <!-- Token Input Fields -->
      <div class="token-container">
        <div class="token-inputs">
          <input
            #tokenInput
            *ngFor="let digit of tokenDigits; let i = index"
            type="text"
            maxlength="1"
            class="token-input"
            [value]="digit"
            (input)="onTokenDigitInput($event, i)"
            (keydown)="onTokenDigitKeydown($event, i)"
            [class.error]="
              tokenForm.get('digit' + (i + 1))?.invalid &&
              tokenForm.get('digit' + (i + 1))?.touched
            "
            autocomplete="one-time-code"
            inputmode="numeric"
            pattern="[0-9]*"
          />
        </div>

        <!-- Error Message -->
        <div class="token-error" *ngIf="tokenForm.invalid && tokenForm.touched">
          <mat-icon class="error-icon">error</mat-icon>
          <span>Please enter a valid 6-digit verification code</span>
        </div>
      </div>

      <!-- Verify Button -->
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="verify-button full-width"
        [disabled]="tokenForm.invalid || isLoading"
      >
        <mat-spinner
          *ngIf="isLoading"
          diameter="20"
          class="button-spinner"
        ></mat-spinner>
        <span *ngIf="!isLoading">Verify Code</span>
        <span *ngIf="isLoading">Verifying...</span>
      </button>

      <!-- Resend Section -->
      <div class="resend-section">
        <p class="resend-text">Didn't receive the code?</p>

        <button
          *ngIf="!canResend"
          mat-button
          type="button"
          class="resend-button disabled"
          disabled
        >
          <mat-icon>schedule</mat-icon>
          Resend in {{ timerDisplay }}
        </button>

        <button
          *ngIf="canResend"
          mat-button
          color="primary"
          type="button"
          class="resend-button"
          (click)="onResendCode()"
          [disabled]="isResending"
        >
          <mat-spinner
            *ngIf="isResending"
            diameter="16"
            class="resend-spinner"
          ></mat-spinner>
          <mat-icon *ngIf="!isResending">refresh</mat-icon>
          <span *ngIf="!isResending">Resend Code</span>
          <span *ngIf="isResending">Sending...</span>
        </button>
      </div>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <mat-divider></mat-divider>
      <div class="back-to-login">
        <button
          mat-button
          color="primary"
          (click)="onBackToLogin()"
          class="back-button"
        >
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </mat-card>
</div>
