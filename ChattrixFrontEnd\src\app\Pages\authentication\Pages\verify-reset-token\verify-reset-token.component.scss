/* Verify Reset Token Component Specific Styles */

.token-card {
  max-width: 420px;
}

.full-width {
  width: 100%;
}

/* Token Input Container */
.token-container {
  margin-bottom: var(--spacing-xl);
}

.token-inputs {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.token-input {
  width: 50px;
  height: 60px;
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-input);
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: 600;
  text-align: center;
  outline: none;
  transition: all var(--transition-normal);

  &:focus {
    border-color: var(--accent-green);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    background-color: var(--bg-secondary);
  }

  &:hover {
    border-color: var(--border-secondary);
  }

  &.error {
    border-color: var(--error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

/* Token Error Message */
.token-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-sm);

  .error-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
}

/* Verify Button */
.verify-button {
  height: 44px;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-transform: none;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xl);
  position: relative;

  &:disabled {
    opacity: 0.6;
  }
}

.button-spinner {
  margin-right: var(--spacing-sm);
}

/* Resend Section */
.resend-section {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.resend-text {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-md);
}

.resend-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  text-transform: none;
  margin: 0 auto;

  &:not(.disabled) {
    color: var(--accent-green) !important;

    &:hover {
      color: var(--accent-green-hover) !important;
      background: transparent;
    }
  }

  &.disabled {
    color: var(--text-disabled) !important;
    cursor: not-allowed;
  }
}

.resend-spinner {
  margin-right: var(--spacing-xs);
}

/* Back to Login */
.back-to-login {
  text-align: center;
  padding-top: var(--spacing-lg);
}

.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--accent-green) !important;
  text-transform: none;
  font-size: var(--font-size-sm);
  margin: 0 auto;

  &:hover {
    color: var(--accent-green-hover) !important;
    background: transparent;
  }
}

/* Logo Fallback Styles */
.logo-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--accent-green) 0%,
    var(--accent-green-light) 100%
  );
  border-radius: 50%;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Snackbar Styles */
::ng-deep .error-snackbar {
  background-color: var(--error) !important;
  color: var(--text-primary) !important;
}

::ng-deep .success-snackbar {
  background-color: var(--success) !important;
  color: var(--text-primary) !important;
}

/* Responsive Design */
@media (max-width: 480px) {
  .token-card {
    margin: var(--spacing-sm);
    max-width: calc(100vw - 2rem);
  }

  .auth-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .auth-form {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .auth-footer {
    padding: var(--spacing-md);
  }

  .auth-logo {
    width: 50px;
    height: 50px;
  }

  .auth-title {
    font-size: var(--font-size-lg);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
  }

  .token-input {
    width: 45px;
    height: 55px;
    font-size: var(--font-size-lg);
  }

  .token-inputs {
    gap: var(--spacing-xs);
  }
}

@media (max-width: 360px) {
  .token-input {
    width: 40px;
    height: 50px;
    font-size: var(--font-size-base);
  }
}

/* Focus and Hover States */
.mat-mdc-button:focus {
  outline: 2px solid var(--accent-green);
  outline-offset: 2px;
}

/* Loading State Animation */
.button-spinner,
.resend-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Token Input Animation */
.token-input {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success State Animation */
.token-input.success {
  animation: successPulse 0.6s ease-in-out;
  border-color: var(--success);
  background-color: rgba(16, 185, 129, 0.1);
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Timer Display Styling */
.resend-button .mat-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
