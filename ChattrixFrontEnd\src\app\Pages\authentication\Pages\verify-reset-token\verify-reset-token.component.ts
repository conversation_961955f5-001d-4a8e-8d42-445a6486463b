import {
  Compo<PERSON>,
  OnInit,
  <PERSON><PERSON><PERSON><PERSON>,
  ElementRef,
  ViewChildren,
  QueryList,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, interval, Subscription } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AuthenticationService } from '../../Services/Authentication.service';
import { AuthStateService } from '../../Services/AuthState.service';
import {
  VerifyResetTokenRequest,
  ForgotPasswordRequest,
  AuthState,
  AuthError,
} from '../../Models';

@Component({
  selector: 'app-verify-reset-token',
  standalone: false,
  templateUrl: './verify-reset-token.component.html',
  styleUrl: './verify-reset-token.component.scss',
})
export class VerifyResetTokenComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChildren('tokenInput') tokenInputs!: QueryList<ElementRef>;

  private destroy$ = new Subject<void>();
  private timerSubscription?: Subscription;

  tokenForm: FormGroup;
  email: string = '';
  logoLoaded = true; // Assume logo loads successfully by default

  // Token state
  tokenDigits: string[] = ['', '', '', '', '', ''];

  // Timer state
  resendTimer = 0;
  canResend = false;

  // Authentication state
  isLoading = false;
  isResending = false;
  error: string | null = null;

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
  ) {
    this.tokenForm = this.formBuilder.group({
      digit1: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit2: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit3: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit4: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit5: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit6: ['', [Validators.required, Validators.pattern(/^\d$/)]],
    });
  }

  ngOnInit(): void {
    // Get email from query parameters
    this.route.queryParams.subscribe((params) => {
      this.email = params['email'];
      if (!this.email) {
        this.router.navigate(['/auth/forgot-password']);
        return;
      }
    });

    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isLoading = state.isLoading;
        this.error = state.error;

        // Show error messages
        if (state.error) {
          this.showError(state.error);
        }
      });

    // Start resend timer
    this.startResendTimer();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.timerSubscription?.unsubscribe();
  }

  onTokenDigitInput(event: any, index: number): void {
    const value = event.target.value;

    if (value && /^\d$/.test(value)) {
      this.tokenDigits[index] = value;

      // Update form control
      this.tokenForm.get(`digit${index + 1}`)?.setValue(value);

      // Move to next input
      if (index < 5) {
        const nextInput = this.tokenInputs.toArray()[index + 1];
        if (nextInput) {
          nextInput.nativeElement.focus();
        }
      }

      // Auto-submit if all digits are filled
      if (this.tokenDigits.every((digit) => digit !== '')) {
        this.onVerifyToken();
      }
    } else if (value === '') {
      this.tokenDigits[index] = '';
      this.tokenForm.get(`digit${index + 1}`)?.setValue('');
    }
  }

  onTokenDigitKeydown(event: KeyboardEvent, index: number): void {
    // Handle backspace
    if (
      event.key === 'Backspace' &&
      this.tokenDigits[index] === '' &&
      index > 0
    ) {
      const prevInput = this.tokenInputs.toArray()[index - 1];
      if (prevInput) {
        prevInput.nativeElement.focus();
      }
    }

    // Handle paste
    if (event.key === 'v' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      navigator.clipboard.readText().then((text) => {
        this.handlePaste(text, index);
      });
    }
  }

  onVerifyToken(): void {
    const token = this.tokenDigits.join('');

    if (token.length === 6 && /^\d{6}$/.test(token)) {
      const verificationData: VerifyResetTokenRequest = {
        email: this.email,
        resetToken: token,
      };

      this.authService.verifyResetToken(verificationData).subscribe({
        next: (response) => {
          if (response.isSuccess) {
            this.showSuccess('Reset token verified successfully!');
            // Navigate to reset password page with email and token
            this.router.navigate(['/auth/reset-password'], {
              queryParams: {
                email: this.email,
                token: token,
              },
            });
          }
        },
        error: (error: AuthError) => {
          console.error('Token verification failed:', error);
          this.clearToken();
        },
      });
    } else {
      this.showError('Please enter a valid 6-digit verification code');
      this.markFormGroupTouched();
    }
  }

  onResendCode(): void {
    if (!this.canResend) return;

    this.isResending = true;
    const resendData: ForgotPasswordRequest = {
      email: this.email,
    };

    this.authService.forgotPassword(resendData).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.showSuccess('Verification code sent successfully!');
          this.clearToken();
          this.startResendTimer();
        }
        this.isResending = false;
      },
      error: (error: AuthError) => {
        console.error('Resend code failed:', error);
        this.isResending = false;
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private handlePaste(text: string, startIndex: number): void {
    const digits = text.replace(/\D/g, '').slice(0, 6 - startIndex);

    for (let i = 0; i < digits.length && startIndex + i < 6; i++) {
      const index = startIndex + i;
      this.tokenDigits[index] = digits[i];
      this.tokenForm.get(`digit${index + 1}`)?.setValue(digits[i]);

      const input = this.tokenInputs.toArray()[index];
      if (input) {
        input.nativeElement.value = digits[i];
      }
    }

    // Focus on the next empty input or the last input
    const nextEmptyIndex = this.tokenDigits.findIndex((digit) => digit === '');
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : 5;
    const focusInput = this.tokenInputs.toArray()[focusIndex];
    if (focusInput) {
      focusInput.nativeElement.focus();
    }
  }

  private clearToken(): void {
    this.tokenDigits = ['', '', '', '', '', ''];
    this.tokenForm.reset();

    // Clear input values and focus first input
    setTimeout(() => {
      this.tokenInputs.toArray().forEach((input, index) => {
        input.nativeElement.value = '';
      });

      const firstInput = this.tokenInputs.toArray()[0];
      if (firstInput) {
        firstInput.nativeElement.focus();
      }
    });
  }

  private startResendTimer(): void {
    this.resendTimer = 60; // 60 seconds
    this.canResend = false;

    this.timerSubscription?.unsubscribe();
    this.timerSubscription = interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.resendTimer--;
        if (this.resendTimer <= 0) {
          this.canResend = true;
          this.timerSubscription?.unsubscribe();
        }
      });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.tokenForm.controls).forEach((key) => {
      const control = this.tokenForm.get(key);
      control?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
    });
  }

  // Getter for timer display
  get timerDisplay(): string {
    const minutes = Math.floor(this.resendTimer / 60);
    const seconds = this.resendTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
