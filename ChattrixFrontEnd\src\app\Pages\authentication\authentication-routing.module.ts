import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { LoginComponent } from './Pages/login/login.component';
import { SignUpComponent } from './Pages/sign-up/sign-up.component';
import { OtpVerificationComponent } from './Pages/otp-verification/otp-verification.component';
import { ForgetPasswordComponent } from './Pages/forget-password/forget-password.component';
import { VerifyResetTokenComponent } from './Pages/verify-reset-token/verify-reset-token.component';
import { ResetPasswordComponent } from './Pages/reset-password/reset-password.component';
import { GuestGuard } from './Guards/guest.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'login',
    component: LoginComponent,
    title: 'Login - Chattrix',
    canActivate: [GuestGuard],
  },
  {
    path: 'signup',
    component: SignUpComponent,
    title: 'Sign Up - Chattrix',
    canActivate: [GuestGuard],
  },
  {
    path: 'otp-verification',
    component: OtpVerificationComponent,
    title: 'Verify Account - Chattrix',
    // Note: OTP verification doesn't use GuestGuard as users might be in intermediate state
  },
  {
    path: 'forgot-password',
    component: ForgetPasswordComponent,
    title: 'Forgot Password - Chattrix',
    canActivate: [GuestGuard],
  },
  {
    path: 'verify-reset-token',
    component: VerifyResetTokenComponent,
    title: 'Verify Reset Code - Chattrix',
    // Note: Verify reset token doesn't use GuestGuard as users are in password reset flow
  },
  {
    path: 'reset-password',
    component: ResetPasswordComponent,
    title: 'Reset Password - Chattrix',
    // Note: Reset password doesn't use GuestGuard as users are in password reset flow
  },
  {
    path: '**',
    redirectTo: 'login',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuthenticationRoutingModule {}
