import {
  Component,
  OnInit,
  OnD<PERSON>roy,
  ElementRef,
  ViewChildren,
  QueryList,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, interval, Subscription } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

import { AuthenticationService } from '../../Services/Authentication.service';
import { AuthStateService } from '../../Services/AuthState.service';
import {
  OtpVerificationRequest,
  ResendOtpRequest,
  AuthState,
  AuthError,
  UserInfo,
} from '../../Models';

@Component({
  selector: 'app-otp-verification',
  standalone: false,
  templateUrl: './otp-verification.component.html',
  styleUrl: './otp-verification.component.scss',
})
export class OtpVerificationComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChildren('otpInput') otpInputs!: QueryList<ElementRef>;

  private destroy$ = new Subject<void>();
  private timerSubscription?: Subscription;

  otpForm: FormGroup;
  userId: string = '';
  logoLoaded = true; // Assume logo loads successfully by default

  // OTP state
  otpDigits: string[] = ['', '', '', '', '', ''];

  // Timer state
  resendTimer = 0;
  canResend = false;

  // Authentication state
  isAuthenticated = false;
  isLoading = false;
  isResending = false;
  user: UserInfo | null = null;
  error: string | null = null;

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
  ) {
    this.otpForm = this.formBuilder.group({
      digit1: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit2: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit3: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit4: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit5: ['', [Validators.required, Validators.pattern(/^\d$/)]],
      digit6: ['', [Validators.required, Validators.pattern(/^\d$/)]],
    });
  }

  ngOnInit(): void {
    // Get userId from query parameters
    this.route.queryParams.subscribe((params) => {
      this.userId = params['userId'];
      if (!this.userId) {
        this.router.navigate(['/auth/login']);
        return;
      }
    });

    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isAuthenticated = state.isAuthenticated;
        this.isLoading = state.isLoading;
        this.user = state.user;
        this.error = state.error;

        // Show error messages
        if (state.error) {
          this.showError(state.error);
        }

        // Redirect if authenticated
        if (state.isAuthenticated && state.user) {
          this.router.navigate(['/dashboard']);
        }
      });

    // Start resend timer
    this.startResendTimer();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.timerSubscription?.unsubscribe();
  }

  onOtpDigitInput(event: any, index: number): void {
    const value = event.target.value;

    if (value && /^\d$/.test(value)) {
      this.otpDigits[index] = value;

      // Update form control
      this.otpForm.get(`digit${index + 1}`)?.setValue(value);

      // Move to next input
      if (index < 5) {
        const nextInput = this.otpInputs.toArray()[index + 1];
        if (nextInput) {
          nextInput.nativeElement.focus();
        }
      }

      // Auto-submit if all digits are filled
      if (this.otpDigits.every((digit) => digit !== '')) {
        this.onVerifyOtp();
      }
    } else if (value === '') {
      this.otpDigits[index] = '';
      this.otpForm.get(`digit${index + 1}`)?.setValue('');
    }
  }

  onOtpDigitKeydown(event: KeyboardEvent, index: number): void {
    // Handle backspace
    if (
      event.key === 'Backspace' &&
      this.otpDigits[index] === '' &&
      index > 0
    ) {
      const prevInput = this.otpInputs.toArray()[index - 1];
      if (prevInput) {
        prevInput.nativeElement.focus();
      }
    }

    // Handle paste
    if (event.key === 'v' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      navigator.clipboard.readText().then((text) => {
        this.handlePaste(text, index);
      });
    }
  }

  onVerifyOtp(): void {
    const otp = this.otpDigits.join('');

    if (otp.length === 6 && /^\d{6}$/.test(otp)) {
      const verificationData: OtpVerificationRequest = {
        userId: this.userId,
        otp: otp,
      };

      this.authService.loginWith2FA(this.userId, otp).subscribe({
        next: (response) => {
          if (response.isSuccess) {
            this.showSuccess('OTP verified successfully!');
            // Navigation will be handled by auth state subscription
          }
        },
        error: (error: AuthError) => {
          console.error('OTP verification failed:', error);
          this.clearOtp();
        },
      });
    } else {
      this.showError('Please enter a valid 6-digit OTP');
      this.markFormGroupTouched();
    }
  }

  onResendOtp(): void {
    if (!this.canResend) return;

    this.isResending = true;
    const resendData: ResendOtpRequest = {
      userId: this.userId,
    };

    this.authService.resendOtp(this.userId).subscribe({
      next: (response) => {
        if (response.isSuccess) {
          this.showSuccess('OTP sent successfully!');
          this.clearOtp();
          this.startResendTimer();
        }
        this.isResending = false;
      },
      error: (error: AuthError) => {
        console.error('Resend OTP failed:', error);
        this.isResending = false;
      },
    });
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private handlePaste(text: string, startIndex: number): void {
    const digits = text.replace(/\D/g, '').slice(0, 6 - startIndex);

    for (let i = 0; i < digits.length && startIndex + i < 6; i++) {
      const index = startIndex + i;
      this.otpDigits[index] = digits[i];
      this.otpForm.get(`digit${index + 1}`)?.setValue(digits[i]);

      const input = this.otpInputs.toArray()[index];
      if (input) {
        input.nativeElement.value = digits[i];
      }
    }

    // Focus on the next empty input or the last input
    const nextEmptyIndex = this.otpDigits.findIndex((digit) => digit === '');
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : 5;
    const focusInput = this.otpInputs.toArray()[focusIndex];
    if (focusInput) {
      focusInput.nativeElement.focus();
    }
  }

  private clearOtp(): void {
    this.otpDigits = ['', '', '', '', '', ''];
    this.otpForm.reset();

    // Clear input values and focus first input
    setTimeout(() => {
      this.otpInputs.toArray().forEach((input, index) => {
        input.nativeElement.value = '';
      });

      const firstInput = this.otpInputs.toArray()[0];
      if (firstInput) {
        firstInput.nativeElement.focus();
      }
    });
  }

  private startResendTimer(): void {
    this.resendTimer = 60; // 60 seconds
    this.canResend = false;

    this.timerSubscription?.unsubscribe();
    this.timerSubscription = interval(1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.resendTimer--;
        if (this.resendTimer <= 0) {
          this.canResend = true;
          this.timerSubscription?.unsubscribe();
        }
      });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.otpForm.controls).forEach((key) => {
      const control = this.otpForm.get(key);
      control?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
    });
  }

  // Getter for timer display
  get timerDisplay(): string {
    const minutes = Math.floor(this.resendTimer / 60);
    const seconds = this.resendTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
