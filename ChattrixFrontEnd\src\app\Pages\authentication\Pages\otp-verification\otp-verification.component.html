<div class="auth-container">
  <mat-card class="auth-card otp-card">
    <!-- Header Section -->
    <div class="auth-header">
      <div class="auth-logo">
        <img
          src="assets/logo/ChatLogo.jpg"
          alt="Chattrix Logo"
          (error)="onImageError($event)"
          [style.display]="logoLoaded ? 'block' : 'none'"
        />
        <div
          class="logo-fallback"
          [style.display]="logoLoaded ? 'none' : 'flex'"
        >
          <span class="logo-text">C</span>
        </div>
      </div>
      <h1 class="auth-title">Verify Your Account</h1>
      <p class="auth-subtitle">
        We've sent a 6-digit verification code to your email address. Please
        enter it below to complete your verification.
      </p>
    </div>

    <!-- OTP Form -->
    <form [formGroup]="otpForm" (ngSubmit)="onVerifyOtp()" class="auth-form">
      <!-- OTP Input Fields -->
      <div class="otp-container">
        <div class="otp-inputs">
          <input
            #otpInput
            *ngFor="let digit of otpDigits; let i = index"
            type="text"
            maxlength="1"
            class="otp-input"
            [value]="digit"
            (input)="onOtpDigitInput($event, i)"
            (keydown)="onOtpDigitKeydown($event, i)"
            [class.error]="
              otpForm.get('digit' + (i + 1))?.invalid &&
              otpForm.get('digit' + (i + 1))?.touched
            "
            autocomplete="one-time-code"
            inputmode="numeric"
            pattern="[0-9]*"
          />
        </div>

        <!-- Error Message -->
        <div class="otp-error" *ngIf="otpForm.invalid && otpForm.touched">
          <mat-icon class="error-icon">error</mat-icon>
          <span>Please enter a valid 6-digit verification code</span>
        </div>
      </div>

      <!-- Verify Button -->
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="verify-button full-width"
        [disabled]="otpForm.invalid || isLoading"
      >
        <mat-spinner
          *ngIf="isLoading"
          diameter="20"
          class="button-spinner"
        ></mat-spinner>
        <span *ngIf="!isLoading">Verify Code</span>
        <span *ngIf="isLoading">Verifying...</span>
      </button>

      <!-- Resend Section -->
      <div class="resend-section">
        <p class="resend-text">Didn't receive the code?</p>

        <button
          *ngIf="!canResend"
          mat-button
          type="button"
          class="resend-button disabled"
          disabled
        >
          <mat-icon>schedule</mat-icon>
          Resend in {{ timerDisplay }}
        </button>

        <button
          *ngIf="canResend"
          mat-button
          color="primary"
          type="button"
          class="resend-button"
          (click)="onResendOtp()"
          [disabled]="isResending"
        >
          <mat-spinner
            *ngIf="isResending"
            diameter="16"
            class="resend-spinner"
          ></mat-spinner>
          <mat-icon *ngIf="!isResending">refresh</mat-icon>
          <span *ngIf="!isResending">Resend Code</span>
          <span *ngIf="isResending">Sending...</span>
        </button>
      </div>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <mat-divider></mat-divider>
      <div class="back-to-login">
        <button
          mat-button
          color="primary"
          (click)="onBackToLogin()"
          class="back-button"
        >
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </mat-card>
</div>
