<div class="auth-container">
  <mat-card class="auth-card">
    <!-- Header Section -->
    <div class="auth-header">
      <div class="auth-logo">
        <img
          src="logo/logo2.png"
          alt="Chattrix Logo"
          (error)="onImageError($event)"
          [style.display]="logoLoaded ? 'block' : 'none'"
        />
        <div
          class="logo-fallback"
          [style.display]="logoLoaded ? 'none' : 'flex'"
        >
          <span class="logo-text">C</span>
        </div>
      </div>
      <h1 class="auth-title">Welcome Back</h1>
      <p class="auth-subtitle">Sign in to your Chattrix account</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="auth-form">
      <!-- Email Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Email Address</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          placeholder="Enter your email"
          autocomplete="email"
        />
        <mat-icon matSuffix>email</mat-icon>
        <mat-error *ngIf="emailControl?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="emailControl?.hasError('email')">
          Please enter a valid email address
        </mat-error>
      </mat-form-field>

      <!-- Password Field -->
      <mat-form-field appearance="fill" class="full-width">
        <mat-label>Password</mat-label>
        <input
          matInput
          [type]="hidePassword ? 'password' : 'text'"
          formControlName="password"
          placeholder="Enter your password"
          autocomplete="current-password"
        />
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="togglePasswordVisibility()"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hidePassword"
        >
          <mat-icon>{{
            hidePassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error *ngIf="passwordControl?.hasError('required')">
          Password is required
        </mat-error>
        <mat-error *ngIf="passwordControl?.hasError('minlength')">
          Password must be at least 6 characters long
        </mat-error>
      </mat-form-field>

      <!-- Forgot Password Link -->
      <div class="forgot-password-container">
        <button
          type="button"
          mat-button
          color="primary"
          (click)="onForgotPassword()"
          class="forgot-password-link"
        >
          Forgot your password?
        </button>
      </div>

      <!-- Login Button -->
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="login-button full-width"
        [disabled]="loginForm.invalid || isLoading"
      >
        <mat-spinner
          *ngIf="isLoading"
          diameter="20"
          class="button-spinner"
        ></mat-spinner>
        <span *ngIf="!isLoading">Sign In</span>
        <span *ngIf="isLoading">Signing In...</span>
      </button>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <mat-divider></mat-divider>
      <p class="signup-prompt">
        Don't have an account?
        <button
          mat-button
          color="primary"
          (click)="onSignUp()"
          class="signup-link"
        >
          Sign Up
        </button>
      </p>
    </div>
  </mat-card>
</div>
